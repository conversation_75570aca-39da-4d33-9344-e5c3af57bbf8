package com.insurfact.ins.repository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Repository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

// Import skynet entity classes
import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Address;
import com.insurfact.skynet.entity.Phone;
import com.insurfact.skynet.entity.Email;
import com.insurfact.skynet.entity.Province;
import com.insurfact.skynet.entity.Country;
import com.insurfact.skynet.entity.Users;

/**
 * Repository class for accessing Advisor Profile data from the database.
 * Uses JdbcTemplate with custom ResultSetExtractor to handle complex joins
 * and one-to-many relationships efficiently in a single query.
 *
 * This repository returns com.insurfact.skynet.entity.Advisor objects
 * populated from database queries, following the revised implementation plan.
 *
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Repository
public class AdvisorProfileRepository {

    private static final Logger log = LoggerFactory.getLogger(AdvisorProfileRepository.class);

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public AdvisorProfileRepository(@Qualifier("skytestJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Finds a complete advisor profile by advisor ID.
     * Executes a single complex query with multiple LEFT JOINs to fetch
     * advisor, contact, address, phone, and email data efficiently.
     *
     * @param advisorId the unique identifier of the advisor
     * @return Advisor entity containing complete profile information, or null if not found
     * @throws DataAccessException if database access fails
     */
    public Advisor findProfileById(Long advisorId) {
        log.debug("Finding advisor profile for ID: {}", advisorId);

        String sql = buildAdvisorProfileQuery();
        
        try {
            Advisor result = jdbcTemplate.query(sql, new Object[]{advisorId}, new AdvisorEntityResultSetExtractor());
            log.debug("Found advisor profile: {}", result != null ? "Yes" : "No");
            return result;
        } catch (DataAccessException e) {
            log.error("Error finding advisor profile for ID {}: {}", advisorId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Builds the complex SQL query for fetching advisor profile data.
     * Includes LEFT JOINs to all related tables to fetch complete information.
     */
    private String buildAdvisorProfileQuery() {
        return """
            SELECT 
                a.ADVISOR_INT_ID,
                a.ADVISOR_CODE,
                a.STATUS,
                a.ADVISOR_TYPE,
                a.CREATION_DATE,
                a.LAST_MODIFICATION_DATE,
                a.NOTES,
                
                c.CONTACT_INT_ID,
                c.FIRST_NAME,
                c.LAST_NAME,
                c.MIDDLE_NAME,
                c.PREFERRED_NAME,
                c.BIRTH_DATE,
                c.SIN,
                c.PREFERRED_LANGUAGE,
                c.GENDER,
                c.SALUTATION,
                c.CONTACT_TYPE,
                
                addr.ADDRESS_INT_ID,
                addr.ADDRESS_TYPE,
                addr.STREET_ADDRESS_1,
                addr.STREET_ADDRESS_2,
                addr.CITY,
                addr.POSTAL_CODE,
                addr.IS_PRIMARY as ADDR_IS_PRIMARY,
                addr.NOTES as ADDR_NOTES,
                
                p_addr.PROVINCE_CODE as ADDR_PROVINCE_CODE,
                p_addr.PROVINCE_NAME as ADDR_PROVINCE_NAME,
                
                country_addr.COUNTRY_CODE as ADDR_COUNTRY_CODE,
                country_addr.COUNTRY_NAME as ADDR_COUNTRY_NAME,
                
                ph.PHONE_INT_ID,
                ph.PHONE_TYPE,
                ph.AREA_CODE,
                ph.PHONE_NUMBER,
                ph.EXTENSION,
                ph.IS_PRIMARY as PHONE_IS_PRIMARY,
                ph.NOTES as PHONE_NOTES,
                
                em.EMAIL_INT_ID,
                em.EMAIL_TYPE,
                em.EMAIL_ADDRESS,
                em.IS_PRIMARY as EMAIL_IS_PRIMARY,
                em.NOTES as EMAIL_NOTES,
                
                u.USER_INT_ID,
                u.USERNAME,
                u.ADVISOR_LOCK
                
            FROM ADVISOR a
            LEFT JOIN CONTACT c ON a.ADVISOR_INT_ID = c.CONTACT_INT_ID
            LEFT JOIN CONTACT_ADDRESS ca ON c.CONTACT_INT_ID = ca.CONTACT_INT_ID
            LEFT JOIN ADDRESS addr ON ca.ADDRESS_INT_ID = addr.ADDRESS_INT_ID
            LEFT JOIN PROVINCE p_addr ON addr.PROVINCE_INT_ID = p_addr.PROVINCE_INT_ID
            LEFT JOIN COUNTRY country_addr ON addr.COUNTRY_INT_ID = country_addr.COUNTRY_INT_ID
            LEFT JOIN CONTACT_PHONE cp ON c.CONTACT_INT_ID = cp.CONTACT_INT_ID
            LEFT JOIN PHONE ph ON cp.PHONE_INT_ID = ph.PHONE_INT_ID
            LEFT JOIN CONTACT_EMAIL ce ON c.CONTACT_INT_ID = ce.CONTACT_INT_ID
            LEFT JOIN EMAIL em ON ce.EMAIL_INT_ID = em.EMAIL_INT_ID
            LEFT JOIN USERS u ON a.ADVISOR_INT_ID = u.ADVISOR_LOCK
            
            WHERE a.ADVISOR_INT_ID = ?
            ORDER BY addr.IS_PRIMARY DESC, ph.IS_PRIMARY DESC, em.IS_PRIMARY DESC
            """;
    }

    /**
     * Custom ResultSetExtractor to handle the complex result set from the advisor profile query.
     * Aggregates multiple rows into a single Advisor entity with nested collections.
     * Creates and populates com.insurfact.skynet.entity objects directly.
     */
    private static class AdvisorEntityResultSetExtractor implements ResultSetExtractor<Advisor> {
        
        @Override
        public Advisor extractData(ResultSet rs) throws SQLException, DataAccessException {
            Advisor advisor = null;
            Contact contact = null;
            Map<Long, Address> addressMap = new HashMap<>();
            Map<Long, Phone> phoneMap = new HashMap<>();
            Map<Long, Email> emailMap = new HashMap<>();
            Users user = null;

            while (rs.next()) {
                // Build advisor object (only once)
                if (advisor == null) {
                    advisor = buildAdvisorEntityFromResultSet(rs);
                }

                // Build contact object (only once)
                if (contact == null && rs.getLong("CONTACT_INT_ID") != 0) {
                    contact = buildContactEntityFromResultSet(rs);
                    advisor.setContact(contact);
                }

                // Build user object (only once)
                if (user == null && rs.getString("USERNAME") != null) {
                    user = buildUserEntityFromResultSet(rs);
                    advisor.setUsers(user);
                }

                // Collect addresses
                Long addressId = rs.getLong("ADDRESS_INT_ID");
                if (addressId != 0 && !addressMap.containsKey(addressId)) {
                    Address address = buildAddressEntityFromResultSet(rs);
                    addressMap.put(addressId, address);
                }

                // Collect phones
                Long phoneId = rs.getLong("PHONE_INT_ID");
                if (phoneId != 0 && !phoneMap.containsKey(phoneId)) {
                    Phone phone = buildPhoneEntityFromResultSet(rs);
                    phoneMap.put(phoneId, phone);
                }

                // Collect emails
                Long emailId = rs.getLong("EMAIL_INT_ID");
                if (emailId != 0 && !emailMap.containsKey(emailId)) {
                    Email email = buildEmailEntityFromResultSet(rs);
                    emailMap.put(emailId, email);
                }
            }

            // Set collections on contact
            if (contact != null) {
                // Note: The exact method names depend on the skynet entity implementation
                // These may need to be adjusted based on the actual entity class structure
                if (!addressMap.isEmpty()) {
                    contact.setAddressList(new ArrayList<>(addressMap.values()));
                }
                if (!phoneMap.isEmpty()) {
                    contact.setPhoneList(new ArrayList<>(phoneMap.values()));
                }
                if (!emailMap.isEmpty()) {
                    contact.setEmailList(new ArrayList<>(emailMap.values()));
                }
            }

            return advisor;
        }

        private Advisor buildAdvisorEntityFromResultSet(ResultSet rs) throws SQLException {
            Advisor advisor = new Advisor();

            // Set advisor fields - adjust field names based on actual entity structure
            advisor.setAdvisorIntId(rs.getLong("ADVISOR_INT_ID"));
            advisor.setAdvisorCode(rs.getString("ADVISOR_CODE"));
            advisor.setStatus(rs.getString("STATUS"));
            advisor.setAdvisorType(rs.getString("ADVISOR_TYPE"));
            advisor.setCreationDate(rs.getTimestamp("CREATION_DATE"));
            advisor.setLastModificationDate(rs.getTimestamp("LAST_MODIFICATION_DATE"));
            advisor.setNotes(rs.getString("NOTES"));

            return advisor;
        }

        private Contact buildContactEntityFromResultSet(ResultSet rs) throws SQLException {
            Contact contact = new Contact();

            // Set contact fields - adjust field names based on actual entity structure
            contact.setContactIntId(rs.getLong("CONTACT_INT_ID"));
            contact.setFirstName(rs.getString("FIRST_NAME"));
            contact.setLastName(rs.getString("LAST_NAME"));
            contact.setMiddleName(rs.getString("MIDDLE_NAME"));
            contact.setPreferredName(rs.getString("PREFERRED_NAME"));
            contact.setBirthDate(rs.getDate("BIRTH_DATE"));
            contact.setSin(rs.getString("SIN")); // Note: SIN masking will be done in DTO mapping
            contact.setPreferredLanguage(rs.getString("PREFERRED_LANGUAGE"));
            contact.setGender(rs.getString("GENDER"));
            contact.setSalutation(rs.getString("SALUTATION"));
            contact.setContactType(rs.getInt("CONTACT_TYPE"));

            return contact;
        }

        private Address buildAddressEntityFromResultSet(ResultSet rs) throws SQLException {
            Address address = new Address();

            // Set address fields
            address.setAddressIntId(rs.getLong("ADDRESS_INT_ID"));
            address.setAddressType(rs.getString("ADDRESS_TYPE"));
            address.setStreetAddress1(rs.getString("STREET_ADDRESS_1"));
            address.setStreetAddress2(rs.getString("STREET_ADDRESS_2"));
            address.setCity(rs.getString("CITY"));
            address.setPostalCode(rs.getString("POSTAL_CODE"));
            address.setIsPrimary(rs.getBoolean("ADDR_IS_PRIMARY"));
            address.setNotes(rs.getString("ADDR_NOTES"));

            // Set province if available
            if (rs.getString("ADDR_PROVINCE_CODE") != null) {
                Province province = new Province();
                province.setProvinceCode(rs.getString("ADDR_PROVINCE_CODE"));
                province.setProvinceName(rs.getString("ADDR_PROVINCE_NAME"));
                address.setProvince(province);
            }

            // Set country if available
            if (rs.getString("ADDR_COUNTRY_CODE") != null) {
                Country country = new Country();
                country.setCountryCode(rs.getString("ADDR_COUNTRY_CODE"));
                country.setCountryName(rs.getString("ADDR_COUNTRY_NAME"));
                address.setCountry(country);
            }

            return address;
        }

        private Phone buildPhoneEntityFromResultSet(ResultSet rs) throws SQLException {
            Phone phone = new Phone();

            // Set phone fields
            phone.setPhoneIntId(rs.getLong("PHONE_INT_ID"));
            phone.setPhoneType(rs.getString("PHONE_TYPE"));
            phone.setAreaCode(rs.getString("AREA_CODE"));
            phone.setPhoneNumber(rs.getString("PHONE_NUMBER"));
            phone.setExtension(rs.getString("EXTENSION"));
            phone.setIsPrimary(rs.getBoolean("PHONE_IS_PRIMARY"));
            phone.setNotes(rs.getString("PHONE_NOTES"));

            return phone;
        }

        private Email buildEmailEntityFromResultSet(ResultSet rs) throws SQLException {
            Email email = new Email();

            // Set email fields
            email.setEmailIntId(rs.getLong("EMAIL_INT_ID"));
            email.setEmailType(rs.getString("EMAIL_TYPE"));
            email.setEmailAddress(rs.getString("EMAIL_ADDRESS"));
            email.setIsPrimary(rs.getBoolean("EMAIL_IS_PRIMARY"));
            email.setNotes(rs.getString("EMAIL_NOTES"));

            return email;
        }

        private Users buildUserEntityFromResultSet(ResultSet rs) throws SQLException {
            Users user = new Users();

            // Set user fields - based on Users entity structure
            user.setUserIntId(rs.getInt("USER_INT_ID"));
            user.setUsername(rs.getString("USERNAME"));
            user.setAdvisorLock(rs.getInt("ADVISOR_LOCK"));
            // Note: Other user fields can be added as needed

            return user;
        }

    }
}
